import { IWebhookHand<PERSON> } from './webhook.handler';
import { PAYMENT_SERVICE_INJECT_TOKENS } from '@shared';
import { RepositoryOptions } from '@cbidigital/aqua-ddd';
import { ILogger, Inject, Lifecycle, Logger, Provider } from '@heronjs/common';
import {
    IRetryUtil,
    IDatabaseUtil,
    GatewaySubscription,
    IWebhookEventBuilder,
    PaymentSucceededEvent,
    ITransactionRepository,
    IPaymentGatewayFactory,
    IWebhookEventRepository,
    TransactionNotFoundError,
    PAYMENT_MODULE_INJECT_TOKENS,
} from '@cbidigital/payment-module';
import {
    ISubscription,
    ISubscriptionRepository,
    MEMBERSHIP_INJECT_TOKENS,
    SubscriptionNotFoundError,
    MissingSubscriptionIdError,
} from '@cbidigital/membership-module';

@Provider({
    token: PAYMENT_SERVICE_INJECT_TOKENS.HANDLER.PAYMENT_SUCCEEDED,
    scope: Lifecycle.Singleton,
})
export class PaymentSucceededHand<PERSON> implements IWebhookHandler {
    private readonly logger: ILogger;

    constructor(
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.BUILDER.WEBHOOK_EVENT)
        protected readonly webhookEventBuilder: IWebhookEventBuilder,
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.REPOSITORY.WEBHOOK_EVENT)
        protected readonly webhookEventRepo: IWebhookEventRepository,
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.REPOSITORY.TRANSACTION)
        protected readonly transactionRepo: ITransactionRepository,
        @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.SUBSCRIPTION)
        protected readonly subsRepo: ISubscriptionRepository,
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.FACTORY.PAYMENT_GATEWAY)
        protected readonly paymentGatewayFactory: IPaymentGatewayFactory,
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.UTIL.DATABASE)
        protected readonly databaseUtil: IDatabaseUtil,
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.UTIL.RETRY)
        protected readonly retryUtil: IRetryUtil,
    ) {
        this.logger = new Logger(this.constructor.name);
    }

    async handle(event: PaymentSucceededEvent) {
        const trx = await this.databaseUtil.startTrx();
        const repoOptions = { trx };
        try {
            const isHandled = await this.isWebhookEventHandled(event, repoOptions);
            if (isHandled) return;
            await Promise.all([
                this.updateSubscription(event, repoOptions),
                this.updateTransaction(event, repoOptions),
                this.saveWebhookEvent(event, repoOptions),
            ]);
            await trx.commit();
        } catch (error) {
            await trx.rollback();
            this.logger.error('Failed to handle payment succeeded', error);
            throw error;
        }
    }

    private async isWebhookEventHandled(event: PaymentSucceededEvent, repoOptions: RepositoryOptions) {
        return this.webhookEventRepo.findOne(
            {
                filter: { eventId: { $eq: event.eventId }, gateway: { $eq: event.gateway } },
            },
            repoOptions,
        );
    }

    private async saveWebhookEvent(event: PaymentSucceededEvent, repoOptions: RepositoryOptions) {
        const webhookEvent = await this.webhookEventBuilder.build();
        await webhookEvent.create({
            eventId: event.eventId,
            gateway: event.gateway,
            eventType: event.type,
            payload: event,
            referenceId: event.data.invoice,
        });
        await this.webhookEventRepo.create(webhookEvent, repoOptions);
    }

    private async updateTransaction(event: PaymentSucceededEvent, repoOptions: RepositoryOptions) {
        const { data } = event;
        const { invoice } = data;
        const maxRetry = 3;
        const retryInterval = 3000;
        console.log('keys of retry util', Object.keys(this.retryUtil));

        const queryTransaction = () =>
            this.transactionRepo.findOne(
                { filter: { gatewayTransactionId: { $eq: invoice }, gateway: { $eq: event.gateway } } },
                repoOptions,
            );

        const transaction = await this.retryUtil.retry(queryTransaction, maxRetry, retryInterval);
        if (!transaction) throw new TransactionNotFoundError();
        transaction.markAsCompleted();
        await this.transactionRepo.update(transaction, repoOptions);
    }

    private async updateSubscription(event: PaymentSucceededEvent, repoOptions: RepositoryOptions) {
        const { data } = event;
        const { appSubscriptionId } = data.metadata;
        if (!appSubscriptionId) throw new MissingSubscriptionIdError();
        // Handle subscription created
        const { subs, gatewaySubs } = await this.fetchSubscription(event, repoOptions);
        await this.updateInvoice(subs, gatewaySubs);
        await subs.activate();
        await this.subsRepo.update(subs, repoOptions);
    }

    private async fetchSubscription(event: PaymentSucceededEvent, repoOptions: RepositoryOptions) {
        const paymentGateway = this.paymentGatewayFactory.get(event.gateway);
        const { appSubscriptionId } = event.data.metadata;
        const [subs, gatewaySubs] = await Promise.all([
            this.subsRepo.findOne({ filter: { id: { $eq: appSubscriptionId } } }, repoOptions),
            paymentGateway.getSubscription(event.data.subscription),
        ]);
        if (!subs || !gatewaySubs) throw new SubscriptionNotFoundError();
        return { subs, gatewaySubs };
    }

    private async updateInvoice(subs: ISubscription, gatewaySubs: GatewaySubscription) {
        const { periodStart, periodEnd } = gatewaySubs;
        const draftInvoice = subs.getDraftInvoice();
        if (draftInvoice) await draftInvoice.markAsPaid({ periodStart, periodEnd });
    }
}
